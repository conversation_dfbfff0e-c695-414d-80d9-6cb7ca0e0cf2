#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实时日志功能的脚本
"""

import sys
import os
import time
import threading

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入主程序的类
from 主程序 import ImageProcessor, log_broadcaster

def test_realtime_logging():
    """测试实时日志功能"""
    processor = ImageProcessor()
    
    def simulate_processing():
        """模拟数据处理过程"""
        processor.log("🚀 开始模拟数据处理...", 'info')
        time.sleep(1)
        
        processor.log("📁 创建输出目录...", 'info')
        time.sleep(0.5)
        
        processor.log("🔍 开始解析HTML内容...", 'info')
        time.sleep(1)
        
        processor.log("✅ 成功解析到 5 条订单", 'success')
        time.sleep(0.5)
        
        processor.log("🔍 开始SKU筛选，只处理共享文件夹中不存在的SKU...", 'info')
        time.sleep(1)
        
        # 模拟检查多个SKU
        for i in range(1, 6):
            processor.log(f"🔍 正在检查SKU: {i}/5 (TEST-SKU-{i:03d})", 'info')
            time.sleep(0.3)
        
        processor.log("📊 筛选结果: 总计 5 个SKU，共享文件夹已有 2 个，需要处理 3 个", 'info')
        time.sleep(0.5)
        
        processor.log("🔍 开始为筛选后的 3 个SKU获取商品名称...", 'info')
        time.sleep(0.5)
        
        # 模拟获取商品名称
        for i in range(1, 4):
            processor.log(f"🔍 处理第 {i}/3 个缺失SKU: TEST-SKU-{i:03d}", 'info')
            time.sleep(0.5)
            processor.log(f"  ✅ 第 {i}/3 个SKU商品名称获取成功: 测试商品 {i}", 'success')
            time.sleep(0.3)
        
        processor.log("📊 缺失SKU商品名称获取统计: 成功 3/3", 'info')
        time.sleep(0.5)
        
        processor.log("📥 开始下载筛选后的商品图片...", 'info')
        time.sleep(0.5)
        
        # 模拟下载图片
        for i in range(1, 4):
            processor.log(f"📥 正在下载第 {i}/3 个商品图片: TEST-SKU-{i:03d}", 'info')
            time.sleep(0.8)
            processor.log(f"✅ 第 {i}/3 个商品图片下载成功: TEST-SKU-{i:03d}", 'success')
            time.sleep(0.2)
        
        processor.log("📊 商品图片下载完成: 成功 3/3", 'info')
        time.sleep(0.5)
        
        processor.log("📁 目录重命名为: 缺失SKU下载_2025年8月18日上午9点15分_共3个", 'info')
        time.sleep(0.5)
        
        processor.log("✅ 数据处理完成，商品图片已下载到: 缺失SKU下载_2025年8月18日上午9点15分_共3个/商品图/", 'success')
        time.sleep(0.5)
        
        # 模拟完成信号
        completion_data = {
            'type': 'completion',
            'success': True,
            'message': '成功获取数据并完成筛选，需要处理 3 个SKU（已跳过共享文件夹中已有的SKU），商品图片已自动下载',
            'products': [
                {
                    'id': 'TEST-SKU-001',
                    'name': '测试商品 1',
                    'sku': 'TEST-SKU-001',
                    'order_id': 'ORDER-001',
                    'order_number': 'ON-001',
                    'quantity': '1',
                    'price': '10.00',
                    'image_url': 'https://example.com/image1.jpg',
                    'local_image_path': './商品图/TEST-SKU-001.jpg'
                },
                {
                    'id': 'TEST-SKU-002',
                    'name': '测试商品 2',
                    'sku': 'TEST-SKU-002',
                    'order_id': 'ORDER-002',
                    'order_number': 'ON-002',
                    'quantity': '2',
                    'price': '20.00',
                    'image_url': 'https://example.com/image2.jpg',
                    'local_image_path': './商品图/TEST-SKU-002.jpg'
                },
                {
                    'id': 'TEST-SKU-003',
                    'name': '测试商品 3',
                    'sku': 'TEST-SKU-003',
                    'order_id': 'ORDER-003',
                    'order_number': 'ON-003',
                    'quantity': '1',
                    'price': '15.00',
                    'image_url': 'https://example.com/image3.jpg',
                    'local_image_path': './商品图/TEST-SKU-003.jpg'
                }
            ],
            'output_dir': '缺失SKU下载_2025年8月18日上午9点15分_共3个'
        }
        
        # 将完成信号发送到所有会话
        for session_queue in log_broadcaster.sessions.values():
            try:
                session_queue.put(completion_data, block=False)
            except:
                pass
        
        processor.log("🎉 测试完成！", 'success')
    
    # 启动模拟处理线程
    thread = threading.Thread(target=simulate_processing)
    thread.daemon = True
    thread.start()
    
    print("测试实时日志功能已启动，请在浏览器中查看效果")
    print("访问 http://localhost:8724 并点击'从API获取数据'按钮")

if __name__ == "__main__":
    test_realtime_logging()
