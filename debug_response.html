<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<input id="dailySaleSetID" type="hidden"  data-sale="0" data-set="0">
<input id="isAllOnCod" type="hidden" value="">
<!--分页-->
<div class="col-xs-12 p0 mTop10" style="text-align:right;min-height:35px;">
                <ul id="upPage" class="pageDiv" style="margin:0 0 4px;"></ul>
    </div>
<div class="col-xs-12 p0">
	<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<style>
    .click-expand{width: 32px;height: 28px;position: absolute;left: 0;top: 0;z-index: 1;}
</style>
<form id="printJhForm" action="" method="POST" target="_blank">
    <input id="pageNo" type="hidden" value="1">
    <input id="totalPage" type="hidden" value="1">
    <input id="pageSize" type="hidden" value="100">
    <input id="totalSize" type="hidden" value="42">
    <input id="orderIdsStr" type="hidden" value="138220103884512868;138220103884512798;138220103884909270;138220103884909036;138220103885272348;138220103885360914;138220103885360846;138220103885621950;138220103885682708;138220103885621858;138220103885621800;138220103885621722;138220103885621634;138220103885884086;138220103886929128;138220103888258280;138220103888664528;138220103888965142;138220103889052390;138220103889352690;138220103889352616;138220103889352522;138220103889352408;138220103889752770;138220103889752664;138220103889752522;138220103889752368;138220103890141712;138220103890215988;138220103890141558;138220103890519458;138220103890215900;138220103890519308;138220103890519208;138220103890890648;138220103890890596;138220103891263236;138220103891263168;138220103891541320;138220103891836182;138220103891836084;138220103892120892">
    <button class="hide" type="button" id="printJhBtn1" onclick="batchPrintPackageHtml(1)">print</button>
    <button class="hide" type="button" id="printJhBtn2" onclick="PrintJhAll(1)">print</button>
    <button class="hide" type="button" id="printJhBtn3" onclick="PrintJhAll(2)">print</button>
    <button class="hide" type="button" id="printJhBtn4" onclick="PrintJhAll(3)">print</button>
    <button class="hide" type="button" id="printJhBtn6" onclick="PrintJhAll(3, 1)">print</button>
    <button class="hide" type="button" id="printJhBtn5" onclick="batchPrintPackageHtml(1,'','',1)">print</button>
    <input type="hidden" id="isDesc" name="isDesc" value=""/>
    <input type="hidden" id="authId" name="authId" value=""/>
    <input type="hidden" id="packageIds" name="packageIds" value=""/>
    <input type="hidden" id="orderField" name="orderField" value=""/>
    <input type="hidden" id="stockProductNum" value="0">
    <table class="myj-table" id="orderListTable" cid="productList">
        <thead>
        <tr>
            <th class="p0-imp minW190">
                <input class="pull-left m-left10" id="selectAll" name="curPage"
                       type="checkbox" selIptId="selectAll"
                       onclick="showSelCheckboxNum(this, 'order');"/>&nbsp;&nbsp;商品信息
            </th>
            <th class="minW90">订单金额</th>
            <th class="minW135">收件人「国家/地区」</th>
            <th class="minW110-imp w180-imp">订单号</th>
            <th class="minW160">时间</th>
            <th>状态</th>
            <th class="border-right2 w70 minW70 maxW70">操作</th>
        </tr>
        </thead>
        <tbody class="xianshishujudate">
        <input type="hidden" id="profitPermission" value="1" /><tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103884512868" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103884512868" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103884512868" data-createtime="2025-08-17 09:53:28.0"
                                   data-packageNumber="XM1XC77030556"/>
                            <input type="hidden" id="trackingNumber_138220103884512868" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103884512868');">XM1XC77030556</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103884512868"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103884512868"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103884512868"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/e520eee8b1de43ebaa945daeec156e90-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/e520eee8b1de43ebaa945daeec156e90-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML61090410</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：33821820661</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103884512868_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103884512868"></span>
                                            <span id="unit_138220103884512868"></span>
                                            <span class="f13" id="profit_138220103884512868">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103884512868">
                                            <span id="pShowTypeVal_138220103884512868"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103884512868" type="hidden" value="PO-211-13235259095670070">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103884512868');">PO-211-13235259095670070</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML61090410<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 09:25</div>
                                <div>付款：2025-08-17 09:25</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103884512868">
                                        <script>addTimer("timeLeft_138220103884512868", 979598,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103884512868');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103884512868');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103884512868');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103884512798" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103884512798" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103884512798" data-createtime="2025-08-17 09:53:26.0"
                                   data-packageNumber="XM1XC77030555"/>
                            <input type="hidden" id="trackingNumber_138220103884512798" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103884512798');">XM1XC77030555</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103884512798"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103884512798"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103884512798"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/f2a9d25f4c31418896570f0d512f1afa-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/f2a9d25f4c31418896570f0d512f1afa-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML59121178</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：80714901032</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103884512798_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103884512798"></span>
                                            <span id="unit_138220103884512798"></span>
                                            <span class="f13" id="profit_138220103884512798">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103884512798">
                                            <span id="pShowTypeVal_138220103884512798"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103884512798" type="hidden" value="PO-211-13600295163512246">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103884512798');">PO-211-13600295163512246</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML59121178<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 09:26</div>
                                <div>付款：2025-08-17 09:26</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103884512798">
                                        <script>addTimer("timeLeft_138220103884512798", 968798,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103884512798');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103884512798');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103884512798');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103884909270" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103884909270" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103884909270" data-createtime="2025-08-17 10:56:53.0"
                                   data-packageNumber="XM1XC77030581"/>
                            <input type="hidden" id="trackingNumber_138220103884909270" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103884909270');">XM1XC77030581</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103884909270"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103884909270"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103884909270"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/916e3daa8314499b8d4dca1069dc9e07-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/916e3daa8314499b8d4dca1069dc9e07-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML61283187</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：22614992173</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/e0e84535ba3842429f979f3a5b907bd9-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/e0e84535ba3842429f979f3a5b907bd9-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML34717825</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanRed">2</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：12771302494</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103884909270_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103884909270"></span>
                                            <span id="unit_138220103884909270"></span>
                                            <span class="f13" id="profit_138220103884909270">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103884909270">
                                            <span id="pShowTypeVal_138220103884909270"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103884909270" type="hidden" value="PO-211-13470859704953530">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103884909270');">PO-211-13470859704953530</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML61283187、ML34717825<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 10:33</div>
                                <div>付款：2025-08-17 10:33</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103884909270">
                                        <script>addTimer("timeLeft_138220103884909270", 968798,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103884909270');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103884909270');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103884909270');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103884909036" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103884909036" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103884909036" data-createtime="2025-08-17 10:56:52.0"
                                   data-packageNumber="XM1XC77030580"/>
                            <input type="hidden" id="trackingNumber_138220103884909036" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103884909036');">XM1XC77030580</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103884909036"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103884909036"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103884909036"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/719f85b986674b1588781da7bdee22ca-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/719f85b986674b1588781da7bdee22ca-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML39508551</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：57864526985</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103884909036_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103884909036"></span>
                                            <span id="unit_138220103884909036"></span>
                                            <span class="f13" id="profit_138220103884909036">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103884909036">
                                            <span id="pShowTypeVal_138220103884909036"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103884909036" type="hidden" value="PO-211-13280308173430346">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103884909036');">PO-211-13280308173430346</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML39508551<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 10:35</div>
                                <div>付款：2025-08-17 10:35</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103884909036">
                                        <script>addTimer("timeLeft_138220103884909036", 968799,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103884909036');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103884909036');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103884909036');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103885272348" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103885272348" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103885272348" data-createtime="2025-08-17 12:01:32.0"
                                   data-packageNumber="XM1XC77030596"/>
                            <input type="hidden" id="trackingNumber_138220103885272348" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103885272348');">XM1XC77030596</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103885272348"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103885272348"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103885272348"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/1c4fded8b1cd44419cf823cbc1ff9ec3-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/1c4fded8b1cd44419cf823cbc1ff9ec3-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML37798802</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：99788755660</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103885272348_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103885272348"></span>
                                            <span id="unit_138220103885272348"></span>
                                            <span class="f13" id="profit_138220103885272348">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103885272348">
                                            <span id="pShowTypeVal_138220103885272348"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103885272348" type="hidden" value="PO-211-13463464837753350">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103885272348');">PO-211-13463464837753350</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML37798802<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 11:21</div>
                                <div>付款：2025-08-17 11:21</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103885272348">
                                        <script>addTimer("timeLeft_138220103885272348", 968799,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103885272348');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103885272348');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103885272348');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103885360914" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103885360914" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="7019798" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103885360914" data-createtime="2025-08-17 12:17:39.0"
                                   data-packageNumber="XM1XC77030611"/>
                            <input type="hidden" id="trackingNumber_138220103885360914" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103885360914');">XM1XC77030611</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103885360914"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管-02」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103885360914"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103885360914"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/8e6bff59eebc4786a3135c077d0d0ff9-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/8e6bff59eebc4786a3135c077d0d0ff9-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML64539434</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：69430155302</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管-02"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103885360914_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103885360914"></span>
                                            <span id="unit_138220103885360914"></span>
                                            <span class="f13" id="profit_138220103885360914">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103885360914">
                                            <span id="pShowTypeVal_138220103885360914"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103885360914" type="hidden" value="PO-211-13113366846071343">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103885360914');">PO-211-13113366846071343</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML64539434<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 11:59</div>
                                <div>付款：2025-08-17 11:59</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103885360914">
                                        <script>addTimer("timeLeft_138220103885360914", 975346,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103885360914');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103885360914');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103885360914');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103885360846" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103885360846" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="7019798" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103885360846" data-createtime="2025-08-17 12:17:38.0"
                                   data-packageNumber="XM1XC77030610"/>
                            <input type="hidden" id="trackingNumber_138220103885360846" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103885360846');">XM1XC77030610</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103885360846"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管-02」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103885360846"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103885360846"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/1c53420496fc4bf686186c50d743933d-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/1c53420496fc4bf686186c50d743933d-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML20509528</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：57408165529</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管-02"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103885360846_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103885360846"></span>
                                            <span id="unit_138220103885360846"></span>
                                            <span class="f13" id="profit_138220103885360846">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103885360846">
                                            <span id="pShowTypeVal_138220103885360846"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103885360846" type="hidden" value="PO-211-13095596365431161">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103885360846');">PO-211-13095596365431161</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML20509528<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 12:04</div>
                                <div>付款：2025-08-17 12:04</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103885360846">
                                        <script>addTimer("timeLeft_138220103885360846", 975273,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103885360846');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103885360846');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103885360846');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103885621950" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103885621950" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103885621950" data-createtime="2025-08-17 13:05:33.0"
                                   data-packageNumber="XM1XC77030622"/>
                            <input type="hidden" id="trackingNumber_138220103885621950" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103885621950');">XM1XC77030622</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103885621950"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103885621950"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103885621950"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/917e3093dd4047f99c29209580faa8fb-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/917e3093dd4047f99c29209580faa8fb-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML41712884</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：45693237012</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103885621950_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103885621950"></span>
                                            <span id="unit_138220103885621950"></span>
                                            <span class="f13" id="profit_138220103885621950">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103885621950">
                                            <span id="pShowTypeVal_138220103885621950"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103885621950" type="hidden" value="PO-211-13099591660151169">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103885621950');">PO-211-13099591660151169</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML41712884<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 12:21</div>
                                <div>付款：2025-08-17 12:21</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103885621950">
                                        <script>addTimer("timeLeft_138220103885621950", 976725,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103885621950');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103885621950');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103885621950');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103885682708" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103885682708" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="7019798" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103885682708" data-createtime="2025-08-17 13:16:39.0"
                                   data-packageNumber="XM1XC77030631"/>
                            <input type="hidden" id="trackingNumber_138220103885682708" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103885682708');">XM1XC77030631</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103885682708"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管-02」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103885682708"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103885682708"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/6ca27f4bd07a4056aeffb0bf5eaa5178-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/6ca27f4bd07a4056aeffb0bf5eaa5178-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML00093509</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：50993208961</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管-02"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103885682708_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103885682708"></span>
                                            <span id="unit_138220103885682708"></span>
                                            <span class="f13" id="profit_138220103885682708">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103885682708">
                                            <span id="pShowTypeVal_138220103885682708"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103885682708" type="hidden" value="PO-211-13143459804791643">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103885682708');">PO-211-13143459804791643</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML00093509<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 12:27</div>
                                <div>付款：2025-08-17 12:27</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103885682708">
                                        <script>addTimer("timeLeft_138220103885682708", 965924,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103885682708');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103885682708');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103885682708');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103885621858" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103885621858" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103885621858" data-createtime="2025-08-17 13:05:32.0"
                                   data-packageNumber="XM1XC77030621"/>
                            <input type="hidden" id="trackingNumber_138220103885621858" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103885621858');">XM1XC77030621</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103885621858"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103885621858"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103885621858"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/917e3093dd4047f99c29209580faa8fb-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/917e3093dd4047f99c29209580faa8fb-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML41712884</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：45693237012</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103885621858_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103885621858"></span>
                                            <span id="unit_138220103885621858"></span>
                                            <span class="f13" id="profit_138220103885621858">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103885621858">
                                            <span id="pShowTypeVal_138220103885621858"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103885621858" type="hidden" value="PO-211-13410268324473157">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103885621858');">PO-211-13410268324473157</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML41712884<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 12:29</div>
                                <div>付款：2025-08-17 12:29</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103885621858">
                                        <script>addTimer("timeLeft_138220103885621858", 965925,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103885621858');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103885621858');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103885621858');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103885621800" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103885621800" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103885621800" data-createtime="2025-08-17 13:05:31.0"
                                   data-packageNumber="XM1XC77030620"/>
                            <input type="hidden" id="trackingNumber_138220103885621800" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103885621800');">XM1XC77030620</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103885621800"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103885621800"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103885621800"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/9611989a6b1140219870f932dfbe6422-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/9611989a6b1140219870f932dfbe6422-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML68467619</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：69712090478</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103885621800_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103885621800"></span>
                                            <span id="unit_138220103885621800"></span>
                                            <span class="f13" id="profit_138220103885621800">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103885621800">
                                            <span id="pShowTypeVal_138220103885621800"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103885621800" type="hidden" value="PO-211-13458513767033380">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103885621800');">PO-211-13458513767033380</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML68467619<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 12:39</div>
                                <div>付款：2025-08-17 12:39</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103885621800">
                                        <script>addTimer("timeLeft_138220103885621800", 965925,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103885621800');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103885621800');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103885621800');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103885621722" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103885621722" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103885621722" data-createtime="2025-08-17 13:05:30.0"
                                   data-packageNumber="XM1XC77030618"/>
                            <input type="hidden" id="trackingNumber_138220103885621722" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103885621722');">XM1XC77030618</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103885621722"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103885621722"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103885621722"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/b21003abb6dd4207bb8d316e443d9e3a-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/b21003abb6dd4207bb8d316e443d9e3a-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML52951274</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanRed">4</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：96299207775</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/71051fa53f5d4b3c876f43bcf2553a42-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/71051fa53f5d4b3c876f43bcf2553a42-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML94433152</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanRed">4</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：63595329453</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/f03f4f88414e43088bf6a69137509fdf-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/f03f4f88414e43088bf6a69137509fdf-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML34883435</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanRed">8</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：29329847564</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103885621722_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103885621722"></span>
                                            <span id="unit_138220103885621722"></span>
                                            <span class="f13" id="profit_138220103885621722">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103885621722">
                                            <span id="pShowTypeVal_138220103885621722"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103885621722" type="hidden" value="PO-211-13546991662713844">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103885621722');">PO-211-13546991662713844</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML52951274、ML94433152、ML34883435<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 12:44</div>
                                <div>付款：2025-08-17 12:44</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103885621722">
                                        <script>addTimer("timeLeft_138220103885621722", 964472,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103885621722');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103885621722');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103885621722');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103885621634" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103885621634" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103885621634" data-createtime="2025-08-17 13:05:29.0"
                                   data-packageNumber="XM1XC77030617"/>
                            <input type="hidden" id="trackingNumber_138220103885621634" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103885621634');">XM1XC77030617</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103885621634"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103885621634"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103885621634"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/2b452d2988944cf485451fb9f1ae50b3-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/2b452d2988944cf485451fb9f1ae50b3-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML09445302</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanRed">2</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：31311472828</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103885621634_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103885621634"></span>
                                            <span id="unit_138220103885621634"></span>
                                            <span class="f13" id="profit_138220103885621634">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103885621634">
                                            <span id="pShowTypeVal_138220103885621634"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103885621634" type="hidden" value="PO-211-13689427692152773">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103885621634');">PO-211-13689427692152773</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML09445302<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 12:44</div>
                                <div>付款：2025-08-17 12:44</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103885621634">
                                        <script>addTimer("timeLeft_138220103885621634", 964473,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103885621634');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103885621634');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103885621634');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103885884086" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103885884086" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103885884086" data-createtime="2025-08-17 13:57:05.0"
                                   data-packageNumber="XM1XC77030635"/>
                            <input type="hidden" id="trackingNumber_138220103885884086" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103885884086');">XM1XC77030635</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103885884086"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103885884086"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103885884086"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/880450a2011a406eba4bf25dfcdc90ad-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/880450a2011a406eba4bf25dfcdc90ad-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML03394378</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：75299903178</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103885884086_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103885884086"></span>
                                            <span id="unit_138220103885884086"></span>
                                            <span class="f13" id="profit_138220103885884086">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103885884086">
                                            <span id="pShowTypeVal_138220103885884086"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103885884086" type="hidden" value="PO-211-13272866663030358">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103885884086');">PO-211-13272866663030358</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML03394378<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 13:19</div>
                                <div>付款：2025-08-17 13:19</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103885884086">
                                        <script>addTimer("timeLeft_138220103885884086", 964472,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103885884086');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103885884086');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103885884086');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103886929128" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103886929128" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103886929128" data-createtime="2025-08-17 17:23:59.0"
                                   data-packageNumber="XM1XC77030657"/>
                            <input type="hidden" id="trackingNumber_138220103886929128" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103886929128');">XM1XC77030657</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103886929128"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103886929128"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103886929128"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/b21003abb6dd4207bb8d316e443d9e3a-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/b21003abb6dd4207bb8d316e443d9e3a-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML52951274</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanRed">2</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：96299207775</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103886929128_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103886929128"></span>
                                            <span id="unit_138220103886929128"></span>
                                            <span class="f13" id="profit_138220103886929128">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103886929128">
                                            <span id="pShowTypeVal_138220103886929128"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103886929128" type="hidden" value="PO-211-13493192335993655">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103886929128');">PO-211-13493192335993655</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML52951274<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 17:17</div>
                                <div>付款：2025-08-17 17:17</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103886929128">
                                        <script>addTimer("timeLeft_138220103886929128", 964472,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103886929128');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103886929128');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103886929128');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103888258280" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103888258280" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103888258280" data-createtime="2025-08-17 21:22:53.0"
                                   data-packageNumber="XM1XC77030671"/>
                            <input type="hidden" id="trackingNumber_138220103888258280" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103888258280');">XM1XC77030671</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103888258280"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103888258280"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103888258280"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/1b695fd320854498a32a596e8c71dd84-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/1b695fd320854498a32a596e8c71dd84-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML64666342</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：32466401852</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103888258280_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103888258280"></span>
                                            <span id="unit_138220103888258280"></span>
                                            <span class="f13" id="profit_138220103888258280">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103888258280">
                                            <span id="pShowTypeVal_138220103888258280"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103888258280" type="hidden" value="PO-211-13322902318710604">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103888258280');">PO-211-13322902318710604</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML64666342<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 20:37</div>
                                <div>付款：2025-08-17 20:37</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103888258280">
                                        <script>addTimer("timeLeft_138220103888258280", 962312,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103888258280');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103888258280');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103888258280');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103888664528" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103888664528" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="7019798" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103888664528" data-createtime="2025-08-17 22:22:38.0"
                                   data-packageNumber="XM1XC77030694"/>
                            <input type="hidden" id="trackingNumber_138220103888664528" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103888664528');">XM1XC77030694</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103888664528"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管-02」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103888664528"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103888664528"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/d7a0e0740be64b4f8cabb6ef48783a91-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/d7a0e0740be64b4f8cabb6ef48783a91-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML64223496</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：18654050868</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管-02"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103888664528_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103888664528"></span>
                                            <span id="unit_138220103888664528"></span>
                                            <span class="f13" id="profit_138220103888664528">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103888664528">
                                            <span id="pShowTypeVal_138220103888664528"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103888664528" type="hidden" value="PO-211-13200011164791848">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103888664528');">PO-211-13200011164791848</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML64223496<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 22:13</div>
                                <div>付款：2025-08-17 22:13</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103888664528">
                                        <script>addTimer("timeLeft_138220103888664528", 965924,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103888664528');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103888664528');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103888664528');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103888965142" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103888965142" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103888965142" data-createtime="2025-08-17 23:04:26.0"
                                   data-packageNumber="XM1XC77030698"/>
                            <input type="hidden" id="trackingNumber_138220103888965142" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103888965142');">XM1XC77030698</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103888965142"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103888965142"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103888965142"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/e0e84535ba3842429f979f3a5b907bd9-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/e0e84535ba3842429f979f3a5b907bd9-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML34717825</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：12771302494</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103888965142_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103888965142"></span>
                                            <span id="unit_138220103888965142"></span>
                                            <span class="f13" id="profit_138220103888965142">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103888965142">
                                            <span id="pShowTypeVal_138220103888965142"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103888965142" type="hidden" value="PO-211-13495963630713668">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103888965142');">PO-211-13495963630713668</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML34717825<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 22:44</div>
                                <div>付款：2025-08-17 22:44</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103888965142">
                                        <script>addTimer("timeLeft_138220103888965142", 968073,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103888965142');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103888965142');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103888965142');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103889052390" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103889052390" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="7019798" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103889052390" data-createtime="2025-08-17 23:16:38.0"
                                   data-packageNumber="XM1XC77030709"/>
                            <input type="hidden" id="trackingNumber_138220103889052390" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103889052390');">XM1XC77030709</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103889052390"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管-02」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103889052390"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103889052390"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/87240920b376417fac8747cb6851baa6-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/87240920b376417fac8747cb6851baa6-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML56256243</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：92977307944</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管-02"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103889052390_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103889052390"></span>
                                            <span id="unit_138220103889052390"></span>
                                            <span class="f13" id="profit_138220103889052390">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103889052390">
                                            <span id="pShowTypeVal_138220103889052390"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103889052390" type="hidden" value="PO-211-13510388862073838">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103889052390');">PO-211-13510388862073838</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML56256243<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 23:06</div>
                                <div>付款：2025-08-17 23:06</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103889052390">
                                        <script>addTimer("timeLeft_138220103889052390", 965913,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103889052390');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103889052390');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103889052390');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103889352690" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103889352690" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103889352690" data-createtime="2025-08-17 23:59:35.0"
                                   data-packageNumber="XM1XC77030719"/>
                            <input type="hidden" id="trackingNumber_138220103889352690" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103889352690');">XM1XC77030719</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103889352690"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103889352690"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103889352690"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/1c4fded8b1cd44419cf823cbc1ff9ec3-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/1c4fded8b1cd44419cf823cbc1ff9ec3-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML37798802</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：99788755660</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103889352690_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103889352690"></span>
                                            <span id="unit_138220103889352690"></span>
                                            <span class="f13" id="profit_138220103889352690">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103889352690">
                                            <span id="pShowTypeVal_138220103889352690"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103889352690" type="hidden" value="PO-211-13676097313912565">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103889352690');">PO-211-13676097313912565</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML37798802<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 23:15</div>
                                <div>付款：2025-08-17 23:15</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103889352690">
                                        <script>addTimer("timeLeft_138220103889352690", 972399,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103889352690');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103889352690');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103889352690');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103889352616" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103889352616" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103889352616" data-createtime="2025-08-17 23:59:34.0"
                                   data-packageNumber="XM1XC77030718"/>
                            <input type="hidden" id="trackingNumber_138220103889352616" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103889352616');">XM1XC77030718</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103889352616"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103889352616"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103889352616"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/75539be4c61a42e6b1bfdb2352e9f3a0-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/75539be4c61a42e6b1bfdb2352e9f3a0-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML37052647</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：42315498617</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103889352616_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103889352616"></span>
                                            <span id="unit_138220103889352616"></span>
                                            <span class="f13" id="profit_138220103889352616">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103889352616">
                                            <span id="pShowTypeVal_138220103889352616"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103889352616" type="hidden" value="PO-211-13402246948473173">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103889352616');">PO-211-13402246948473173</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML37052647<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 23:30</div>
                                <div>付款：2025-08-17 23:30</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103889352616">
                                        <script>addTimer("timeLeft_138220103889352616", 969513,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103889352616');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103889352616');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103889352616');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103889352522" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103889352522" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103889352522" data-createtime="2025-08-17 23:59:33.0"
                                   data-packageNumber="XM1XC77030717"/>
                            <input type="hidden" id="trackingNumber_138220103889352522" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103889352522');">XM1XC77030717</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103889352522"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103889352522"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103889352522"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/0bf513399d8f4990bd9d1af8755e6dec-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/0bf513399d8f4990bd9d1af8755e6dec-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML64440491</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：78897598199</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103889352522_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103889352522"></span>
                                            <span id="unit_138220103889352522"></span>
                                            <span class="f13" id="profit_138220103889352522">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103889352522">
                                            <span id="pShowTypeVal_138220103889352522"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103889352522" type="hidden" value="PO-211-13697060959352694">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103889352522');">PO-211-13697060959352694</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML64440491<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 23:34</div>
                                <div>付款：2025-08-17 23:34</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103889352522">
                                        <script>addTimer("timeLeft_138220103889352522", 964473,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103889352522');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103889352522');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103889352522');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103889352408" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103889352408" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103889352408" data-createtime="2025-08-17 23:59:32.0"
                                   data-packageNumber="XM1XC77030716"/>
                            <input type="hidden" id="trackingNumber_138220103889352408" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103889352408');">XM1XC77030716</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103889352408"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103889352408"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103889352408"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/ebea99638bbb4120b47f03247e424cd7-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/ebea99638bbb4120b47f03247e424cd7-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML02625069</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：96423543425</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103889352408_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103889352408"></span>
                                            <span id="unit_138220103889352408"></span>
                                            <span class="f13" id="profit_138220103889352408">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103889352408">
                                            <span id="pShowTypeVal_138220103889352408"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103889352408" type="hidden" value="PO-211-13220208489591952">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103889352408');">PO-211-13220208489591952</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML02625069<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-17 23:42</div>
                                <div>付款：2025-08-17 23:42</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103889352408">
                                        <script>addTimer("timeLeft_138220103889352408", 969525,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103889352408');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103889352408');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103889352408');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103889752770" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103889752770" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103889752770" data-createtime="2025-08-18 00:57:25.0"
                                   data-packageNumber="XM1XC77030743"/>
                            <input type="hidden" id="trackingNumber_138220103889752770" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103889752770');">XM1XC77030743</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103889752770"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103889752770"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103889752770"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/f9da66378f8e4e838e93cfdc048f4d07-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/f9da66378f8e4e838e93cfdc048f4d07-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML92666122</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：18630255662</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103889752770_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103889752770"></span>
                                            <span id="unit_138220103889752770"></span>
                                            <span class="f13" id="profit_138220103889752770">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103889752770">
                                            <span id="pShowTypeVal_138220103889752770"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103889752770" type="hidden" value="PO-211-13325355986550559">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103889752770');">PO-211-13325355986550559</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML92666122<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 00:04</div>
                                <div>付款：2025-08-18 00:04</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103889752770">
                                        <script>addTimer("timeLeft_138220103889752770", 965925,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103889752770');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103889752770');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103889752770');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103889752664" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103889752664" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103889752664" data-createtime="2025-08-18 00:57:24.0"
                                   data-packageNumber="XM1XC77030742"/>
                            <input type="hidden" id="trackingNumber_138220103889752664" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103889752664');">XM1XC77030742</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103889752664"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103889752664"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103889752664"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/968b9753fce94927ac47bd8a912b5d0d-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/968b9753fce94927ac47bd8a912b5d0d-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML30140313</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：37738153973</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103889752664_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103889752664"></span>
                                            <span id="unit_138220103889752664"></span>
                                            <span class="f13" id="profit_138220103889752664">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103889752664">
                                            <span id="pShowTypeVal_138220103889752664"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103889752664" type="hidden" value="PO-211-13711890383992863">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103889752664');">PO-211-13711890383992863</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML30140313<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 00:10</div>
                                <div>付款：2025-08-18 00:10</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103889752664">
                                        <script>addTimer("timeLeft_138220103889752664", 964473,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103889752664');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103889752664');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103889752664');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103889752522" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103889752522" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103889752522" data-createtime="2025-08-18 00:57:23.0"
                                   data-packageNumber="XM1XC77030741"/>
                            <input type="hidden" id="trackingNumber_138220103889752522" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103889752522');">XM1XC77030741</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103889752522"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103889752522"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103889752522"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/e300bef6b46d47beb0de03459bda3714-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/e300bef6b46d47beb0de03459bda3714-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML25199553</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：25721021099</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103889752522_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103889752522"></span>
                                            <span id="unit_138220103889752522"></span>
                                            <span class="f13" id="profit_138220103889752522">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103889752522">
                                            <span id="pShowTypeVal_138220103889752522"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103889752522" type="hidden" value="PO-211-13281239598710340">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103889752522');">PO-211-13281239598710340</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML25199553<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 00:18</div>
                                <div>付款：2025-08-18 00:18</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103889752522">
                                        <script>addTimer("timeLeft_138220103889752522", 964472,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103889752522');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103889752522');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103889752522');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103889752368" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103889752368" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103889752368" data-createtime="2025-08-18 00:57:22.0"
                                   data-packageNumber="XM1XC77030740"/>
                            <input type="hidden" id="trackingNumber_138220103889752368" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103889752368');">XM1XC77030740</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103889752368"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103889752368"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103889752368"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/e300bef6b46d47beb0de03459bda3714-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/e300bef6b46d47beb0de03459bda3714-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML25199553</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：25721021099</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103889752368_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103889752368"></span>
                                            <span id="unit_138220103889752368"></span>
                                            <span class="f13" id="profit_138220103889752368">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103889752368">
                                            <span id="pShowTypeVal_138220103889752368"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103889752368" type="hidden" value="PO-211-13351258156150693">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103889752368');">PO-211-13351258156150693</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML25199553<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 00:53</div>
                                <div>付款：2025-08-18 00:53</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103889752368">
                                        <script>addTimer("timeLeft_138220103889752368", 965925,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103889752368');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103889752368');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103889752368');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103890141712" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103890141712" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103890141712" data-createtime="2025-08-18 01:55:04.0"
                                   data-packageNumber="XM1XC77030762"/>
                            <input type="hidden" id="trackingNumber_138220103890141712" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103890141712');">XM1XC77030762</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103890141712"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103890141712"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103890141712"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/917e3093dd4047f99c29209580faa8fb-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/917e3093dd4047f99c29209580faa8fb-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML41712884</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：45693237012</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103890141712_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103890141712"></span>
                                            <span id="unit_138220103890141712"></span>
                                            <span class="f13" id="profit_138220103890141712">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103890141712">
                                            <span id="pShowTypeVal_138220103890141712"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103890141712" type="hidden" value="PO-211-13154146890871578">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103890141712');">PO-211-13154146890871578</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML41712884<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 01:22</div>
                                <div>付款：2025-08-18 01:22</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103890141712">
                                        <script>addTimer("timeLeft_138220103890141712", 964473,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103890141712');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103890141712');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103890141712');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103890215988" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103890215988" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="7019798" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103890215988" data-createtime="2025-08-18 02:10:56.0"
                                   data-packageNumber="XM1XC77030774"/>
                            <input type="hidden" id="trackingNumber_138220103890215988" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103890215988');">XM1XC77030774</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103890215988"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管-02」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103890215988"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103890215988"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/57645670942d47e7a75d514b769caa49-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/57645670942d47e7a75d514b769caa49-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML44995859</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：38670359475</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管-02"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103890215988_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103890215988"></span>
                                            <span id="unit_138220103890215988"></span>
                                            <span class="f13" id="profit_138220103890215988">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103890215988">
                                            <span id="pShowTypeVal_138220103890215988"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103890215988" type="hidden" value="PO-211-13285931717750312">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103890215988');">PO-211-13285931717750312</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML44995859<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 01:28</div>
                                <div>付款：2025-08-18 01:28</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103890215988">
                                        <script>addTimer("timeLeft_138220103890215988", 964473,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103890215988');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103890215988');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103890215988');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103890141558" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103890141558" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103890141558" data-createtime="2025-08-18 01:55:03.0"
                                   data-packageNumber="XM1XC77030761"/>
                            <input type="hidden" id="trackingNumber_138220103890141558" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103890141558');">XM1XC77030761</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103890141558"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103890141558"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103890141558"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/da82911101a1436e8b41e1566dba4db2-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/da82911101a1436e8b41e1566dba4db2-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML43874980</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：17142700528</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103890141558_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103890141558"></span>
                                            <span id="unit_138220103890141558"></span>
                                            <span class="f13" id="profit_138220103890141558">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103890141558">
                                            <span id="pShowTypeVal_138220103890141558"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103890141558" type="hidden" value="PO-211-13167843569271784">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103890141558');">PO-211-13167843569271784</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML43874980<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 01:30</div>
                                <div>付款：2025-08-18 01:30</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103890141558">
                                        <script>addTimer("timeLeft_138220103890141558", 968073,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103890141558');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103890141558');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103890141558');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103890519458" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103890519458" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103890519458" data-createtime="2025-08-18 03:03:23.0"
                                   data-packageNumber="XM1XC77030782"/>
                            <input type="hidden" id="trackingNumber_138220103890519458" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103890519458');">XM1XC77030782</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103890519458"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103890519458"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103890519458"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/3f8bfee535ad4d4c98dd763b648be0a7-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/3f8bfee535ad4d4c98dd763b648be0a7-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML91594285</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：32960160104</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103890519458_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103890519458"></span>
                                            <span id="unit_138220103890519458"></span>
                                            <span class="f13" id="profit_138220103890519458">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103890519458">
                                            <span id="pShowTypeVal_138220103890519458"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103890519458" type="hidden" value="PO-211-13721250870392839">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103890519458');">PO-211-13721250870392839</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML91594285<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 01:55</div>
                                <div>付款：2025-08-18 01:55</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103890519458">
                                        <script>addTimer("timeLeft_138220103890519458", 969525,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103890519458');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103890519458');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103890519458');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103890215900" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103890215900" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="7019798" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103890215900" data-createtime="2025-08-18 02:10:55.0"
                                   data-packageNumber="XM1XC77030773"/>
                            <input type="hidden" id="trackingNumber_138220103890215900" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103890215900');">XM1XC77030773</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103890215900"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管-02」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103890215900"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103890215900"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/8e6bff59eebc4786a3135c077d0d0ff9-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/8e6bff59eebc4786a3135c077d0d0ff9-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML64539434</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：69430155302</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管-02"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103890215900_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103890215900"></span>
                                            <span id="unit_138220103890215900"></span>
                                            <span class="f13" id="profit_138220103890215900">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103890215900">
                                            <span id="pShowTypeVal_138220103890215900"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103890215900" type="hidden" value="PO-211-13438669438073210">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103890215900');">PO-211-13438669438073210</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML64539434<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 02:03</div>
                                <div>付款：2025-08-18 02:03</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103890215900">
                                        <script>addTimer("timeLeft_138220103890215900", 965924,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103890215900');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103890215900');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103890215900');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103890519308" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103890519308" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103890519308" data-createtime="2025-08-18 03:03:21.0"
                                   data-packageNumber="XM1XC77030781"/>
                            <input type="hidden" id="trackingNumber_138220103890519308" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103890519308');">XM1XC77030781</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103890519308"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103890519308"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103890519308"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/1b695fd320854498a32a596e8c71dd84-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/1b695fd320854498a32a596e8c71dd84-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML64666342</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：32466401852</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103890519308_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103890519308"></span>
                                            <span id="unit_138220103890519308"></span>
                                            <span class="f13" id="profit_138220103890519308">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103890519308">
                                            <span id="pShowTypeVal_138220103890519308"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103890519308" type="hidden" value="PO-211-13727634652793057">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103890519308');">PO-211-13727634652793057</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML64666342<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 02:23</div>
                                <div>付款：2025-08-18 02:23</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103890519308">
                                        <script>addTimer("timeLeft_138220103890519308", 969524,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103890519308');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103890519308');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103890519308');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103890519208" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103890519208" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103890519208" data-createtime="2025-08-18 03:03:20.0"
                                   data-packageNumber="XM1XC77030780"/>
                            <input type="hidden" id="trackingNumber_138220103890519208" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103890519208');">XM1XC77030780</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103890519208"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103890519208"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103890519208"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/ba40cb62a6f541a18e379bc2081344cc-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/ba40cb62a6f541a18e379bc2081344cc-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML79575303</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：23990265611</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103890519208_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103890519208"></span>
                                            <span id="unit_138220103890519208"></span>
                                            <span class="f13" id="profit_138220103890519208">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103890519208">
                                            <span id="pShowTypeVal_138220103890519208"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103890519208" type="hidden" value="PO-211-13190935936631870">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103890519208');">PO-211-13190935936631870</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML79575303<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 02:29</div>
                                <div>付款：2025-08-18 02:29</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103890519208">
                                        <script>addTimer("timeLeft_138220103890519208", 965925,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103890519208');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103890519208');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103890519208');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103890890648" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103890890648" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103890890648" data-createtime="2025-08-18 04:06:41.0"
                                   data-packageNumber="XM1XC77030806"/>
                            <input type="hidden" id="trackingNumber_138220103890890648" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103890890648');">XM1XC77030806</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103890890648"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103890890648"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103890890648"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/880450a2011a406eba4bf25dfcdc90ad-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/880450a2011a406eba4bf25dfcdc90ad-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML03394378</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：75299903178</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103890890648_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103890890648"></span>
                                            <span id="unit_138220103890890648"></span>
                                            <span class="f13" id="profit_138220103890890648">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103890890648">
                                            <span id="pShowTypeVal_138220103890890648"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103890890648" type="hidden" value="PO-211-13437815094393249">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103890890648');">PO-211-13437815094393249</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML03394378<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 03:05</div>
                                <div>付款：2025-08-18 03:05</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103890890648">
                                        <script>addTimer("timeLeft_138220103890890648", 962312,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103890890648');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103890890648');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103890890648');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103890890596" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103890890596" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103890890596" data-createtime="2025-08-18 04:06:40.0"
                                   data-packageNumber="XM1XC77030804"/>
                            <input type="hidden" id="trackingNumber_138220103890890596" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103890890596');">XM1XC77030804</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103890890596"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103890890596"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103890890596"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/829510eff2344abbbb343b59bf422709-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/829510eff2344abbbb343b59bf422709-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML99905139</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：17531329614</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103890890596_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103890890596"></span>
                                            <span id="unit_138220103890890596"></span>
                                            <span class="f13" id="profit_138220103890890596">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103890890596">
                                            <span id="pShowTypeVal_138220103890890596"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103890890596" type="hidden" value="PO-211-13194088504951884">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103890890596');">PO-211-13194088504951884</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML99905139<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 03:39</div>
                                <div>付款：2025-08-18 03:39</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103890890596">
                                        <script>addTimer("timeLeft_138220103890890596", 968072,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103890890596');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103890890596');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103890890596');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103891263236" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103891263236" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="7019798" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103891263236" data-createtime="2025-08-18 05:10:36.0"
                                   data-packageNumber="XM1XC77030838"/>
                            <input type="hidden" id="trackingNumber_138220103891263236" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103891263236');">XM1XC77030838</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103891263236"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管-02」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103891263236"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103891263236"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/0f160170ab494b8cb453d52829aeba42-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/0f160170ab494b8cb453d52829aeba42-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML45949498</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：78207556401</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管-02"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103891263236_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103891263236"></span>
                                            <span id="unit_138220103891263236"></span>
                                            <span class="f13" id="profit_138220103891263236">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103891263236">
                                            <span id="pShowTypeVal_138220103891263236"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103891263236" type="hidden" value="PO-211-13435029691513233">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103891263236');">PO-211-13435029691513233</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML45949498<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 04:17</div>
                                <div>付款：2025-08-18 04:17</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103891263236">
                                        <script>addTimer("timeLeft_138220103891263236", 964472,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103891263236');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103891263236');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103891263236');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103891263168" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103891263168" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="7019798" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103891263168" data-createtime="2025-08-18 05:10:35.0"
                                   data-packageNumber="XM1XC77030837"/>
                            <input type="hidden" id="trackingNumber_138220103891263168" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103891263168');">XM1XC77030837</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103891263168"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管-02」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103891263168"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103891263168"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/8e6bff59eebc4786a3135c077d0d0ff9-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/8e6bff59eebc4786a3135c077d0d0ff9-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML64539434</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：69430155302</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管-02"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103891263168_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103891263168"></span>
                                            <span id="unit_138220103891263168"></span>
                                            <span class="f13" id="profit_138220103891263168">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103891263168">
                                            <span id="pShowTypeVal_138220103891263168"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103891263168" type="hidden" value="PO-211-13062982159991099">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103891263168');">PO-211-13062982159991099</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML64539434<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 04:34</div>
                                <div>付款：2025-08-18 04:34</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103891263168">
                                        <script>addTimer("timeLeft_138220103891263168", 965924,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103891263168');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103891263168');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103891263168');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103891541320" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103891541320" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="7019798" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103891541320" data-createtime="2025-08-18 06:04:50.0"
                                   data-packageNumber="XM1XC77030857"/>
                            <input type="hidden" id="trackingNumber_138220103891541320" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103891541320');">XM1XC77030857</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103891541320"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管-02」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103891541320"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103891541320"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/df5b1c2fef8e4f48ae8635fc39dcef6a-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/df5b1c2fef8e4f48ae8635fc39dcef6a-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML90808649</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：32066823589</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管-02"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103891541320_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103891541320"></span>
                                            <span id="unit_138220103891541320"></span>
                                            <span class="f13" id="profit_138220103891541320">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103891541320">
                                            <span id="pShowTypeVal_138220103891541320"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103891541320" type="hidden" value="PO-211-13637705526392537">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103891541320');">PO-211-13637705526392537</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML90808649<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 05:49</div>
                                <div>付款：2025-08-18 05:49</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103891541320">
                                        <script>addTimer("timeLeft_138220103891541320", 965925,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103891541320');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103891541320');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103891541320');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103891836182" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103891836182" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103891836182" data-createtime="2025-08-18 06:51:49.0"
                                   data-packageNumber="XM1XC77030863"/>
                            <input type="hidden" id="trackingNumber_138220103891836182" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103891836182');">XM1XC77030863</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103891836182"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103891836182"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103891836182"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/0cfb7ad7c2f847bbbaee66ac0868b8cd-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/0cfb7ad7c2f847bbbaee66ac0868b8cd-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML81739239</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：20110161981</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103891836182_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103891836182"></span>
                                            <span id="unit_138220103891836182"></span>
                                            <span class="f13" id="profit_138220103891836182">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103891836182">
                                            <span id="pShowTypeVal_138220103891836182"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103891836182" type="hidden" value="PO-211-13589154176632059">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103891836182');">PO-211-13589154176632059</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML81739239<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 06:03</div>
                                <div>付款：2025-08-18 06:03</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103891836182">
                                        <script>addTimer("timeLeft_138220103891836182", 968798,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103891836182');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103891836182');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103891836182');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103891836084" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103891836084" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103891836084" data-createtime="2025-08-18 06:51:48.0"
                                   data-packageNumber="XM1XC77030862"/>
                            <input type="hidden" id="trackingNumber_138220103891836084" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103891836084');">XM1XC77030862</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103891836084"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103891836084"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103891836084"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/50a19e72a62648ec86121101f3287d4c-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/50a19e72a62648ec86121101f3287d4c-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML53459253</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：66226901656</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103891836084_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103891836084"></span>
                                            <span id="unit_138220103891836084"></span>
                                            <span class="f13" id="profit_138220103891836084">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103891836084">
                                            <span id="pShowTypeVal_138220103891836084"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103891836084" type="hidden" value="PO-211-13435486520953263">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103891836084');">PO-211-13435486520953263</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML53459253<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 06:19</div>
                                <div>付款：2025-08-18 06:19</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103891836084">
                                        <script>addTimer("timeLeft_138220103891836084", 968799,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103891836084');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103891836084');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103891836084');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103892120892" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103892120892" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6833815" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103892120892" data-createtime="2025-08-18 07:44:59.0"
                                   data-packageNumber="XM1XC77030880"/>
                            <input type="hidden" id="trackingNumber_138220103892120892" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103892120892');">XM1XC77030880</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103892120892"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：丽生-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103892120892"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103892120892"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/62106dfa825746c1a12881b5365f7502-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/62106dfa825746c1a12881b5365f7502-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">ML72378099</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：76013884178</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="丽生-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103892120892_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103892120892"></span>
                                            <span id="unit_138220103892120892"></span>
                                            <span class="f13" id="profit_138220103892120892">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103892120892">
                                            <span id="pShowTypeVal_138220103892120892"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103892120892" type="hidden" value="PO-211-13236818243190095">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103892120892');">PO-211-13236818243190095</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：ML72378099<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-18 07:08</div>
                                <div>付款：2025-08-18 07:08</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103892120892">
                                        <script>addTimer("timeLeft_138220103892120892", 964473,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103892120892');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103892120892');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103892120892');">详情</a></td>
                            </tr>
                    </tbody>
    </table>
</form>
<script type="text/javascript">
    publicHoverTextCanBeMovedIn('.blacklistTruggerHover');
    $('.dataTriggerHover').popover({
        trigger: 'hover'
    });
    $('.dataTriggerClick').popover({
        trigger: 'click'
    });
    //鼠标点击位置如果不是popver，popverhide;
    $(document).off('click.orderPopover', 'body').on('click.orderPopover', 'body', function (e) {
        if(!e) e = window.event;

        //获取事件点击元素
        var targ = e.target,
            //获取元素名称
            popover = $(targ).closest('.popover').length,
            $popover = $('.popover'),
            popoverFa = $(targ).hasClass('dataTriggerClick');

        if(popover !== 1 && !popoverFa && $popover.length){
            $('[aria-describedby]').popover('hide'); //触发气泡事件把气泡移除
            $popover.remove(); //可能出现匹配不上未被移除的气泡，所以如果有未移除的气泡再移除一次
        }
    });
    $('[data-toggle="tooltip"]').tooltip();
    $('.amazonlatestDeliveryDate[data-toggle="popover"]').popover();
    //公用悬停显示文案可移入
    publicHoverTextCanBeMovedIn('.hoverShowCommonBrandMsg');
    // 注册买家备注气泡
    publicHoverTextCanBeMovedIn('.buyerNotes', 'top', true);
    expand_click3();
    $(document).ready(function(){
        // console.log('')
        CUSTOM_MARK.initView(1);
        var objs = $('.errorMsgHide');
        $.each(objs,function(i,j){
            var html = $(j).html();
            var arry = '';
            if(html){
                arry = strFilterUrl(html);
            }
            arry = arry.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/alert/g, '提示').replace(/location.href/g, '跳转到：');
            arry = removeScriptContent(arry); // 移出script标签
            arry = removeStyleContent(arry); // 移出style标签
            $(j).closest('.errorMsgBox').find('span.errorMsgShow').append(arry);
        });

        //创建商品
        var create = function (obj, info) {
            var $create = $(obj[0]).find('input.create'),
                $pair = $(obj[0]).find('input.pair'),
                id = $pair.attr('data-id'),
                sku = $create.attr('displaysku'),
                pid = $create.attr('pid'),
                vid = $create.attr('vid'),
                dataObj = {};
            if (!vid) {
                vid = pid;
            }
            dataObj.id = id;
            dataObj.sku = sku;
            dataObj.pid = pid;
            dataObj.vid = vid;

           comOpenWinPost('dxmCommodityProduct/viewCommodityProductFromOrder.htm', {orderInfo: JSON.stringify(dataObj)});
        };
        //配对
        var pair = function(obj,info){
            var $pair = $(obj[0]).find("input.pair");
            showDialogWareHoseProductList($pair.attr('data-id'),$pair.attr('data-index'),
                $pair.attr("data-state"),$pair.attr("data-overseawarehose"),$pair.attr("data-authid"),obj[0]);
        };
        //添加来源
        var addSource = function (obj) {
            var $pair = $(obj[0]).find('input.pair');
            getSourceUrl($pair.attr('data-id'),$pair.attr('data-index'));
        };
        //寻找货源
        var findLink = function(obj,info) {
            var imgUrl = info.imgUrl;
            imgUrl = imgUrl.replace('-tiny.jpg','-original.jpg').replace('_50x50.jpg','').replace('_80x80q80.jpg','').replace('.80x80','').replace('_100x100q100.jpg','_800x800q100.jpg');
            imgUrl = imgUrl.replace('$_6.', '$_19.').replace('.cloudfront.net/image/150_150', '.cloudfront.net/image/400_400');
            PURCHASE_SOURCE.getAlibabaListMethod(imgUrl);
        };
        //淘宝找货源
        var taoBaoFindLink = function(obj, info) {
            var platform = $(obj).find('.create').attr('platform'),
                url = $(obj).closest('tr').find('a.productUrl').attr('href');
            if (!platform && url && url.indexOf('aliexpress.com') !== -1)  platform = 'smt';

            // url = url && (platform === 'wish' || platform === 'ebay' || platform === 'amazon') ? 'productUrl='+url : 'imgUrl='+info.imgUrl;
            var imgUrl = info.imgUrl;
            imgUrl = imgUrl.replace('-tiny.jpg','-original.jpg').replace('_50x50.jpg','').replace('_80x80q80.jpg','').replace('.80x80','').replace('_100x100q100.jpg','_800x800q100.jpg');
            imgUrl = imgUrl.replace('$_6.', '$_19.').replace('.cloudfront.net/image/150_150', '.cloudfront.net/image/400_400');
            ORDER_SOURCE_FN.sourceDetail(imgUrl);
        };
        // 支持刊登的平台，用来判断图片是否显示添加来源
        var mayPublishPlatform = ['wish', 'smt', 'ebay', 'amazon', 'lazada', 'alibaba', 'fanno', 'shopify', 'dh', 'shopee', 'joom', 'tophatter', 'shoplazza', 'tiktok'];
        //注册图片放大
        // console.log('//注册图片放大')
        $('div.listImgIn').each(function(i, j) {
            $(j).off('mouseenter').on('mouseenter',function(event) {
            var $listImgIn = $(this);
            var stockProductNumvar = $('#stockProductNum').val();
            var creates = {
                name:'<i class="glyphicon glyphicon-edit"></i>创建',//必传
                type:'left', //必传
                call: create
            };
            var pairs =  {
                name:'<i class="glyphicon glyphicon-random"></i>配对',
                type:'left',
                call: pair
            };
            var addSources =  {
                name: '添加来源',
                type: 'left',
                call: addSource
            };
            var proId = $listImgIn.find('.create').attr('proid');
            if(proId){
                pairs.name = '<i class="glyphicon glyphicon-random"></i>更换';
            }
            var source1 = {//1688
                name:'<i class="glyphicon glyphicon-search"></i>找货源 1688',
                type:'right',
                call: findLink
            },
            source2 = {//淘宝
                name:'淘宝',
                type:'right',
                call: taoBaoFindLink
            };
            var type = $.trim($listImgIn.attr('data-type')),
                isCreate = $.trim($listImgIn.attr('data-hasProductId')),
                action = [],
                platform = $listImgIn.closest('tr[data-platform]').attr('data-platform');


            if (isCreate == "0" && stockProductNumvar == 1) {
                action.push(creates);
            }
            if(type == "1" || type == "0" && stockProductNumvar == 1) {
                action.push(pairs);
            }
            // 如果有来源链接权限
            if($('#orderSourceUrlType').val() === '1'){
                // 属于支持刊登的平台并且有配对才显示添加来源
                if(mayPublishPlatform.indexOf(platform) !== -1 && proId && (type === '1' || type === '0' && (+stockProductNumvar) === 1)){
                    action.push(addSources);
                }
            }
            action.push(source1);
            action.push(source2);
            $(this).extensiblePicZoom({
                type: 'order',
                divWidth: "350",
                divHeight:"350",
                action: action,
                initToShow: true,
                isHasTaoBao: true//是否加淘宝入口
            });
            $listImgIn = null;
            })
        });
        
            /*新版注册懒加载*/
            $('.imgCss.lazy').newLazyload({
                placeholder: "//www.dianxiaomi.com/static/img/loading5.gif",
                threshold: 1000,
                effect: "fadeIn",
                failurelimit: 20,
                skip_invisible: false
            });
        MYJ_PAGINATION.init(MYJ_PAGINATION.get([30, 50, 100, 300]), '', function(option) {
            if(+option.pageSize !== +$('#pageSize').val()) {
                typeof ORDER_STORAGE_SET !== 'undefined' && typeof ORDER_STORAGE_SET.set === 'function' && ORDER_STORAGE_SET.set('pageSize', option.pageSize)
            }
            pageReload(option);
            $('.normalBtnGroup').removeClass('myj-hide');
            $('.printBtnGroup').addClass('myj-hide');
        });
        MYJ_PAGINATION.init(MYJ_PAGINATION.get([30, 50, 100, 300], true), '#ceilingPage', function(option) {
            if(+option.pageSize !== +$('#pageSize').val()) {
                typeof ORDER_STORAGE_SET !== 'undefined' && typeof ORDER_STORAGE_SET.set === 'function' && ORDER_STORAGE_SET.set('pageSize', option.pageSize)
            }
            pageReload(option);
            $('.normalBtnGroup').removeClass('myj-hide');
            $('.printBtnGroup').addClass('myj-hide');
        });

        var $orderPairNameIsOverlength = $('.orderPairNameIsOverlength'),
            orderPairNameIsOverlengthHtml = '<div class="word-box order-word-box"><div class="whiteBar"></div>' +
                '<div class="word-in wordbox scroll-bar"></div>' +
                '<div class="triangle"></div></div>';
        $.each($orderPairNameIsOverlength, function (i, j) { //循环订单的商品信息
            var $obj = $(j);
            if($obj.height() > 80){ //判断当前信息节点高度是否超过80，如果超过了则表示当前内容超过4行显示了
                $obj.addClass('hover-prompt relative').find('.isOverLengThHide').addClass('no-new-line4'); //加超出4行隐藏class
                $obj.off('mouseover').on('mouseover',function () { //给当前节点加悬停气泡
                    var yanModalObj = $(orderPairNameIsOverlengthHtml),
                        newStr = $(this).find('.isOverLengThHide').html();
                    if ($(this).find('.word-box').length < 1) {
                        yanModalObj.find('.wordbox').append(newStr);
                        $(this).append(yanModalObj);
                    }
                });
            }
        });

        //不再关注按钮显示隐藏
        hasPutHoldMethod();
    });
</script>
</div>

<!-- 分页 -->
<div class="col-xs-12 p0" style="text-align:right;">
        <ul id="downPage" class="pageDiv" style="margin:2px 0px 2px 0;"></ul>
    </div>
<script type="text/javascript">
	$(document).ready(function(){
        var orderIdsStr='138220103884512868;138220103884512798;138220103884909270;138220103884909036;138220103885272348;138220103885360914;138220103885360846;138220103885621950;138220103885682708;138220103885621858;138220103885621800;138220103885621722;138220103885621634;138220103885884086;138220103886929128;138220103888258280;138220103888664528;138220103888965142;138220103889052390;138220103889352690;138220103889352616;138220103889352522;138220103889352408;138220103889752770;138220103889752664;138220103889752522;138220103889752368;138220103890141712;138220103890215988;138220103890141558;138220103890519458;138220103890215900;138220103890519308;138220103890519208;138220103890890648;138220103890890596;138220103891263236;138220103891263168;138220103891541320;138220103891836182;138220103891836084;138220103892120892';
        //获取当前页所有订单的id
        if(orderIdsStr !== ""){
            var orderIds= [],
                $keepState = $('#keepState');
            if(orderIdsStr.indexOf(";")){
                orderIds = orderIdsStr.split(";");
                $keepState.data("orderIds", orderIds);
                $keepState.data("currOrderNum",-1);//当前订单索引
            }
        }
        // 悬浮弹窗的渲染
        publicHoverTextCanBeMovedIn('.delayTips', 'top', false, 350, 200);

    });

    // 翻页请求
    function pageReload(screenObj){
        var url = "package/list.htm?pageNo="+screenObj.pageNo+"&pageSize="+screenObj.pageSize+"&shopId=-1&state=paid&authId=-1&country=&platform=&isSearch=0" +
            "&startTime=&endTime=&orderField=order_pay_time&isVoided=0&isRemoved=0&ruleId=-1&sysRule=&applyType=" +
            "&applyStatus=&printJh=-1&printMd=-1&commitPlatform=&productStatus=&jhComment=-1&storageId=0" +
            "&history=&custom=-1&isOversea=-1&timeOut=0&refundStatus=0&forbiddenStatus=-1&forbiddenReason=0&behindTrack=-1",
            $shopGroupId = $('#shopGroupId'),
            shopGroupId = $shopGroupId.length ? $.trim($shopGroupId.val()) : '';

        if (shopGroupId) url += '&shopGroupId=' + shopGroupId;//如果选中了店铺分组，则加上店铺分组参数作为筛选条件
        MYJ.ajax({
            type: 'GET',
            url: url,
            data: {},
            success: function(data){
                try {
                    clearPageListMemory();
                } catch (e) {
                    console.log('src/main/webapp/WEB-INF/jsp/order/dxmPackageAjax.jsp')
                }
                $('#dxmBody').html(data);
                $('#selectedData').html('');
                refreshApprovedCountNum();
                refreshOrderProfit(0, 'packageId');
                refreshWarehouseInfo('packageId');
            }
        });
        $('#keepState').data({
            'ajaxurl': url,
            'ajaxpostdata': null,
            'pagesize': screenObj.pageSize
        });
    }
  	// 当前显示的是哪个状态(未处理、异常订单、待发货等)
 	var seledStateName = $("#keepState").data("stateName"),
       orderApplyStatus=$('#keepState').data('applystatus');
  	$("#currState").html(seledStateName);

  	// 显示 一键填充COD
    var isAllOnCod = $('#isAllOnCod').val();
    if (isAllOnCod === 'true' || isAllOnCod === true) {
        $('.butchUpdateCOD').removeClass('hide');
    } else {
        $('.butchUpdateCOD').addClass('hide');
    }
    // 小秘云仓-失败的才展示cod
    if (orderApplyStatus !== 'fail') {
        $('.batchCodFail').addClass('hide');
    }

    $(document).off('click','.operateBox .selectionRange ul li');
    $(document).on('click','.operateBox .selectionRange ul li',function(){
        var that = $(this),
            selectVal = that.attr('data-value');
        if(selectVal){
            that.closest('.operateBox').find('.operationResult .resultNumRange').html(selectVal).click();
        }
    });
    // 灰度显隐
    GRAY_AUTH_ALL && GRAY_AUTH_ALL.initDisplay();
</script>
